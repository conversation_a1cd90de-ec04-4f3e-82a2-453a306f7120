package edu.tsinghua.studentmis.student;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 学生管理服务启动类
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@SpringBootApplication(
    scanBasePackages = {"edu.tsinghua.studentmis.student"},
    exclude = {
        org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration.class,
        org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration.class,
        com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.class
    }
)
@EnableDiscoveryClient
@MapperScan("edu.tsinghua.studentmis.student.mapper")
public class StudentApplication {

    public static void main(String[] args) {
        SpringApplication.run(StudentApplication.class, args);
        System.out.println("==========================================");
        System.out.println("  StudentMIS V2 Student Service Started  ");
        System.out.println("  清华大学级学生成绩管理系统 - 学生服务   ");
        System.out.println("==========================================");
    }
}
