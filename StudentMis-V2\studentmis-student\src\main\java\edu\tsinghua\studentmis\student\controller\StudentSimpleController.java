package edu.tsinghua.studentmis.student.controller;

import edu.tsinghua.studentmis.student.common.Result;
import edu.tsinghua.studentmis.student.entity.StudentSimple;
import edu.tsinghua.studentmis.student.service.StudentSimpleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学生信息控制器（简化版）
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/students/simple")
@RequiredArgsConstructor
@Tag(name = "学生信息管理（简化版）", description = "学生基本信息的增删改查操作")
public class StudentSimpleController {

    private final StudentSimpleService studentSimpleService;

    @GetMapping("/test")
    @Operation(summary = "系统测试接口", description = "测试清华大学学生管理服务是否正常运行")
    public Result<String> test() {
        log.info("系统测试接口被调用");
        return Result.success("清华大学学生成绩管理系统 V2.0 - 学生管理服务运行正常！");
    }

    @GetMapping
    @Operation(summary = "查询所有学生信息", description = "获取计算机4241班所有学生的基本信息列表")
    public Result<List<StudentSimple>> getAllStudents() {
        log.info("查询所有学生信息");
        try {
            List<StudentSimple> students = studentSimpleService.getAllStudents();
            return Result.success("成功获取学生信息列表", students);
        } catch (Exception e) {
            log.error("查询学生信息失败", e);
            return Result.error("查询学生信息失败：" + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询学生", description = "根据学生数据库ID获取学生详细信息")
    public Result<StudentSimple> getStudentById(@PathVariable Long id) {
        log.info("根据ID查询学生: {}", id);
        try {
            StudentSimple student = studentSimpleService.getStudentById(id);
            if (student != null) {
                return Result.success("成功获取学生信息", student);
            } else {
                return Result.notFound("未找到ID为 " + id + " 的学生信息");
            }
        } catch (Exception e) {
            log.error("根据ID查询学生失败: {}", id, e);
            return Result.error("查询学生信息失败：" + e.getMessage());
        }
    }

    @GetMapping("/by-student-id/{studentId}")
    @Operation(summary = "根据学号查询学生", description = "根据学号获取学生详细信息")
    public Result<StudentSimple> getStudentByStudentId(@PathVariable String studentId) {
        log.info("根据学号查询学生: {}", studentId);
        try {
            StudentSimple student = studentSimpleService.getStudentByStudentId(studentId);
            if (student != null) {
                return Result.success("成功获取学生信息", student);
            } else {
                return Result.notFound("未找到学号为 " + studentId + " 的学生信息");
            }
        } catch (Exception e) {
            log.error("根据学号查询学生失败: {}", studentId, e);
            return Result.error("查询学生信息失败：" + e.getMessage());
        }
    }
}
