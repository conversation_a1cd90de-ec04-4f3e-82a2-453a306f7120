package edu.tsinghua.studentmis.student.mapper;

import edu.tsinghua.studentmis.student.entity.StudentSimple;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 学生信息Mapper接口（简化版）
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Mapper
public interface StudentSimpleMapper {

    /**
     * 查询所有学生
     */
    @Select("SELECT id, student_id as studentId, name, phone, email FROM stu_basic_info LIMIT 10")
    List<StudentSimple> selectAll();

    /**
     * 根据ID查询学生
     */
    @Select("SELECT id, student_id as studentId, name, phone, email FROM stu_basic_info WHERE id = #{id}")
    StudentSimple selectById(Long id);

    /**
     * 根据学号查询学生
     */
    @Select("SELECT id, student_id as studentId, name, phone, email FROM stu_basic_info WHERE student_id = #{studentId}")
    StudentSimple selectByStudentId(String studentId);
}
