package edu.tsinghua.studentmis.student.controller;

import edu.tsinghua.studentmis.student.entity.StudentSimple;
import edu.tsinghua.studentmis.student.service.StudentSimpleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学生信息控制器（简化版）
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/students/simple")
@RequiredArgsConstructor
@Tag(name = "学生信息管理（简化版）", description = "学生基本信息的增删改查操作")
public class StudentSimpleController {

    private final StudentSimpleService studentSimpleService;

    @GetMapping("/test")
    @Operation(summary = "测试接口", description = "测试服务是否正常运行")
    public String test() {
        log.info("测试接口被调用");
        return "StudentMIS V2 - 学生管理服务运行正常！";
    }

    @GetMapping
    @Operation(summary = "查询所有学生", description = "获取所有学生的基本信息列表")
    public List<StudentSimple> getAllStudents() {
        log.info("查询所有学生信息");
        return studentSimpleService.getAllStudents();
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询学生", description = "根据学生ID获取学生详细信息")
    public StudentSimple getStudentById(@PathVariable Long id) {
        log.info("根据ID查询学生: {}", id);
        return studentSimpleService.getStudentById(id);
    }

    @GetMapping("/by-student-id/{studentId}")
    @Operation(summary = "根据学号查询学生", description = "根据学号获取学生详细信息")
    public StudentSimple getStudentByStudentId(@PathVariable String studentId) {
        log.info("根据学号查询学生: {}", studentId);
        return studentSimpleService.getStudentByStudentId(studentId);
    }
}
