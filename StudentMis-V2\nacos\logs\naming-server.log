2025-06-19 19:26:11,022 INFO [SelectorManager] Load SelectorContextBuilder(class com.alibaba.nacos.naming.selector.context.CmdbSelectorContextBuilder) contextType(CMDB) successfully.

2025-06-19 19:26:11,022 INFO [SelectorManager] Load SelectorContextBuilder(class com.alibaba.nacos.naming.selector.context.NoneSelectorContextBuilder) contextType(NONE) successfully.

2025-06-19 19:26:11,023 INFO [SelectorManager] Load Selector(class com.alibaba.nacos.naming.selector.LabelSelector) type(label) contextType(CMDB) successfully.

2025-06-19 19:26:11,023 INFO [SelectorManager] Load Selector(class com.alibaba.nacos.naming.selector.NoneSelector) type(none) contextType(NONE) successfully.

2025-06-19 19:26:11,898 INFO Load instance extension handler []

2025-06-19 19:27:55,653 INFO [SelectorManager] Load SelectorContextBuilder(class com.alibaba.nacos.naming.selector.context.CmdbSelectorContextBuilder) contextType(CMDB) successfully.

2025-06-19 19:27:55,653 INFO [SelectorManager] Load SelectorContextBuilder(class com.alibaba.nacos.naming.selector.context.NoneSelectorContextBuilder) contextType(NONE) successfully.

2025-06-19 19:27:55,655 INFO [SelectorManager] Load Selector(class com.alibaba.nacos.naming.selector.LabelSelector) type(label) contextType(CMDB) successfully.

2025-06-19 19:27:55,655 INFO [SelectorManager] Load Selector(class com.alibaba.nacos.naming.selector.NoneSelector) type(none) contextType(NONE) successfully.

2025-06-19 19:27:56,322 INFO Load instance extension handler []

2025-06-19 20:11:01,801 INFO [SelectorManager] Load SelectorContextBuilder(class com.alibaba.nacos.naming.selector.context.CmdbSelectorContextBuilder) contextType(CMDB) successfully.

2025-06-19 20:11:01,801 INFO [SelectorManager] Load SelectorContextBuilder(class com.alibaba.nacos.naming.selector.context.NoneSelectorContextBuilder) contextType(NONE) successfully.

2025-06-19 20:11:01,802 INFO [SelectorManager] Load Selector(class com.alibaba.nacos.naming.selector.LabelSelector) type(label) contextType(CMDB) successfully.

2025-06-19 20:11:01,802 INFO [SelectorManager] Load Selector(class com.alibaba.nacos.naming.selector.NoneSelector) type(none) contextType(NONE) successfully.

2025-06-19 20:11:02,609 INFO Load instance extension handler []

2025-06-19 20:14:46,963 INFO Client connection 1750335284315_127.0.0.1_60342 disconnect, remove instances and subscribers

2025-06-19 20:17:27,334 INFO [SelectorManager] Load SelectorContextBuilder(class com.alibaba.nacos.naming.selector.context.CmdbSelectorContextBuilder) contextType(CMDB) successfully.

2025-06-19 20:17:27,334 INFO [SelectorManager] Load SelectorContextBuilder(class com.alibaba.nacos.naming.selector.context.NoneSelectorContextBuilder) contextType(NONE) successfully.

2025-06-19 20:17:27,335 INFO [SelectorManager] Load Selector(class com.alibaba.nacos.naming.selector.LabelSelector) type(label) contextType(CMDB) successfully.

2025-06-19 20:17:27,335 INFO [SelectorManager] Load Selector(class com.alibaba.nacos.naming.selector.NoneSelector) type(none) contextType(NONE) successfully.

2025-06-19 20:17:28,062 INFO Load instance extension handler []

2025-06-19 20:17:57,720 INFO Client connection 1750335477712_127.0.0.1_60961 connect

2025-06-19 20:17:57,841 INFO Client change for service Service{namespace='studentmis-v2', group='DEFAULT_GROUP', name='studentmis-gateway', ephemeral=true, revision=0}, 1750335477712_127.0.0.1_60961

2025-06-19 20:17:58,333 INFO Client connection 1750335477712_127.0.0.1_60961 disconnect, remove instances and subscribers

2025-06-19 20:18:11,135 INFO Client connection 1750335474412_127.0.0.1_60954 disconnect, remove instances and subscribers

2025-06-19 20:18:48,069 INFO Client connection 1750335528062_127.0.0.1_61155 connect

2025-06-19 20:18:48,193 INFO Client change for service Service{namespace='studentmis-v2', group='DEFAULT_GROUP', name='studentmis-gateway', ephemeral=true, revision=2}, 1750335528062_127.0.0.1_61155

2025-06-19 20:18:48,660 INFO Client connection 1750335528062_127.0.0.1_61155 disconnect, remove instances and subscribers

2025-06-19 20:18:59,447 INFO Client connection 1750335524887_127.0.0.1_61138 disconnect, remove instances and subscribers

2025-06-19 20:19:42,649 INFO Client connection 1750335582639_127.0.0.1_61332 connect

2025-06-19 20:19:42,763 INFO Client change for service Service{namespace='studentmis-v2', group='DEFAULT_GROUP', name='studentmis-gateway', ephemeral=true, revision=4}, 1750335582639_127.0.0.1_61332

2025-06-19 20:20:09,193 INFO Client remove for service Service{namespace='studentmis-v2', group='DEFAULT_GROUP', name='studentmis-gateway', ephemeral=true, revision=5}, 1750335582639_127.0.0.1_61332

2025-06-19 20:20:09,203 INFO Client connection 1750335582639_127.0.0.1_61332 disconnect, remove instances and subscribers

2025-06-19 20:20:09,729 INFO Client connection 1750335579437_127.0.0.1_61325 disconnect, remove instances and subscribers

2025-06-19 20:21:35,508 INFO Client connection 1750335693765_127.0.0.1_61658 disconnect, remove instances and subscribers

2025-06-19 20:21:57,939 WARN namespace : studentmis-v2, [DEFAULT_GROUP@@studentmis-gateway] services are automatically cleaned

2025-06-19 20:22:10,095 INFO Client connection 1750335728392_127.0.0.1_61761 disconnect, remove instances and subscribers

2025-06-19 20:23:13,073 INFO Client connection 1750335793063_127.0.0.1_61971 connect

2025-06-19 20:23:13,194 INFO Client change for service Service{namespace='studentmis-v2', group='DEFAULT_GROUP', name='studentmis-gateway', ephemeral=true, revision=0}, 1750335793063_127.0.0.1_61971

2025-06-19 20:23:26,177 INFO Client remove for service Service{namespace='studentmis-v2', group='DEFAULT_GROUP', name='studentmis-gateway', ephemeral=true, revision=1}, 1750335793063_127.0.0.1_61971

2025-06-19 20:23:26,189 INFO Client connection 1750335793063_127.0.0.1_61971 disconnect, remove instances and subscribers

2025-06-19 20:23:26,720 INFO Client connection 1750335789885_127.0.0.1_61961 disconnect, remove instances and subscribers

2025-06-19 20:24:19,640 INFO Client connection 1750335859633_127.0.0.1_62146 connect

2025-06-19 20:24:19,753 INFO Client change for service Service{namespace='studentmis-v2', group='DEFAULT_GROUP', name='studentmis-gateway', ephemeral=true, revision=2}, 1750335859633_127.0.0.1_62146

2025-06-19 20:27:06,305 INFO [SelectorManager] Load SelectorContextBuilder(class com.alibaba.nacos.naming.selector.context.CmdbSelectorContextBuilder) contextType(CMDB) successfully.

2025-06-19 20:27:06,305 INFO [SelectorManager] Load SelectorContextBuilder(class com.alibaba.nacos.naming.selector.context.NoneSelectorContextBuilder) contextType(NONE) successfully.

2025-06-19 20:27:06,306 INFO [SelectorManager] Load Selector(class com.alibaba.nacos.naming.selector.LabelSelector) type(label) contextType(CMDB) successfully.

2025-06-19 20:27:06,306 INFO [SelectorManager] Load Selector(class com.alibaba.nacos.naming.selector.NoneSelector) type(none) contextType(NONE) successfully.

2025-06-19 20:27:06,938 INFO Load instance extension handler []

2025-06-19 20:28:08,273 INFO Client connection 1750336088267_127.0.0.1_62554 connect

2025-06-19 20:28:08,403 INFO Client change for service Service{namespace='studentmis-v2', group='DEFAULT_GROUP', name='studentmis-gateway', ephemeral=true, revision=0}, 1750336088267_127.0.0.1_62554

2025-06-19 20:29:51,823 INFO Client connection 1750336190122_127.0.0.1_62852 disconnect, remove instances and subscribers

2025-06-19 20:30:17,568 INFO Client connection 1750336215904_127.0.0.1_62931 disconnect, remove instances and subscribers

2025-06-19 20:33:12,404 INFO Client connection 1750336390727_127.0.0.1_63523 disconnect, remove instances and subscribers

