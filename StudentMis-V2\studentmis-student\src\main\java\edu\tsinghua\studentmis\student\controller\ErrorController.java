package edu.tsinghua.studentmis.student.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 自定义错误控制器 - 中文错误页面
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Slf4j
@Controller
public class CustomErrorController implements ErrorController {

    private static final String ERROR_PATH = "/error";

    @RequestMapping(ERROR_PATH)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> handleError(HttpServletRequest request) {
        Map<String, Object> errorResponse = new HashMap<>();
        
        // 获取错误状态码
        Integer statusCode = (Integer) request.getAttribute("jakarta.servlet.error.status_code");
        String errorMessage = (String) request.getAttribute("jakarta.servlet.error.message");
        String requestUri = (String) request.getAttribute("jakarta.servlet.error.request_uri");
        
        if (statusCode == null) {
            statusCode = 500;
        }
        
        // 设置中文错误信息
        String chineseMessage = getChineseErrorMessage(statusCode);
        
        errorResponse.put("timestamp", System.currentTimeMillis());
        errorResponse.put("status", statusCode);
        errorResponse.put("error", getErrorType(statusCode));
        errorResponse.put("message", chineseMessage);
        errorResponse.put("path", requestUri);
        errorResponse.put("system", "清华大学学生成绩管理系统 V2.0");
        
        log.error("系统错误 - 状态码: {}, 路径: {}, 错误信息: {}", statusCode, requestUri, errorMessage);
        
        return ResponseEntity.status(statusCode).body(errorResponse);
    }
    
    /**
     * 获取中文错误信息
     */
    private String getChineseErrorMessage(int statusCode) {
        switch (statusCode) {
            case 400:
                return "请求参数错误，请检查输入的数据格式";
            case 401:
                return "未授权访问，请先登录系统";
            case 403:
                return "权限不足，无法访问该资源";
            case 404:
                return "请求的资源不存在，请检查访问路径";
            case 405:
                return "请求方法不被允许";
            case 500:
                return "服务器内部错误，请联系系统管理员";
            case 502:
                return "网关错误，服务暂时不可用";
            case 503:
                return "服务暂时不可用，请稍后重试";
            default:
                return "系统发生未知错误，错误代码: " + statusCode;
        }
    }
    
    /**
     * 获取错误类型
     */
    private String getErrorType(int statusCode) {
        if (statusCode >= 400 && statusCode < 500) {
            return "客户端错误";
        } else if (statusCode >= 500) {
            return "服务器错误";
        } else {
            return "未知错误";
        }
    }
}
