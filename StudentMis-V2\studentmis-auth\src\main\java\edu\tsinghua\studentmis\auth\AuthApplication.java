package edu.tsinghua.studentmis.auth;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 认证服务启动类
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@SpringBootApplication(
    scanBasePackages = {"edu.tsinghua.studentmis.auth"},
    exclude = {
        org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration.class,
        org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration.class,
        com.alibaba.cloud.nacos.NacosConfigAutoConfiguration.class,
        com.alibaba.cloud.nacos.discovery.NacosDiscoveryAutoConfiguration.class,
        com.alibaba.cloud.nacos.discovery.NacosDiscoveryClientConfiguration.class,
        com.alibaba.cloud.nacos.discovery.reactive.NacosReactiveDiscoveryClientConfiguration.class,
        com.alibaba.cloud.nacos.endpoint.NacosConfigEndpointAutoConfiguration.class,
        com.alibaba.cloud.nacos.registry.NacosServiceRegistryAutoConfiguration.class,
        org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration.class
    }
)
// @EnableDiscoveryClient  // 临时禁用进行诊断
@MapperScan(basePackages = "edu.tsinghua.studentmis.auth.mapper")
public class AuthApplication {

    public static void main(String[] args) {
        SpringApplication.run(AuthApplication.class, args);
        System.out.println("========================================");
        System.out.println("  StudentMIS V2 Auth Service Started  ");
        System.out.println("  清华大学级学生成绩管理系统 - 认证服务  ");
        System.out.println("========================================");
    }
}
