package edu.tsinghua.studentmis.student.service;

import edu.tsinghua.studentmis.student.entity.StudentSimple;
import edu.tsinghua.studentmis.student.mapper.StudentSimpleMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 学生信息服务类（简化版）
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StudentSimpleService {

    private final StudentSimpleMapper studentSimpleMapper;

    /**
     * 查询所有学生
     */
    public List<StudentSimple> getAllStudents() {
        log.info("查询所有学生信息");
        return studentSimpleMapper.selectAll();
    }

    /**
     * 根据ID查询学生
     */
    public StudentSimple getStudentById(Long id) {
        log.info("根据ID查询学生: {}", id);
        return studentSimpleMapper.selectById(id);
    }

    /**
     * 根据学号查询学生
     */
    public StudentSimple getStudentByStudentId(String studentId) {
        log.info("根据学号查询学生: {}", studentId);
        return studentSimpleMapper.selectByStudentId(studentId);
    }
}
