server:
  port: 8082

spring:
  application:
    name: studentmis-student
  profiles:
    active: dev
  config:
    import: "optional:nacos:studentmis-student.yml"
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        group: DEFAULT_GROUP
      config:
        server-addr: localhost:8848
        group: DEFAULT_GROUP
        file-extension: yml
        import-check:
          enabled: false
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************
    username: root
    password: 123456
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# 日志配置
logging:
  level:
    edu.tsinghua.studentmis: debug
