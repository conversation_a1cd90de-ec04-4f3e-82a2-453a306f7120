package edu.tsinghua.studentmis.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * API网关启动类
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@SpringBootApplication(
    exclude = {
        com.alibaba.cloud.nacos.NacosConfigAutoConfiguration.class,
        com.alibaba.cloud.nacos.discovery.NacosDiscoveryAutoConfiguration.class,
        com.alibaba.cloud.nacos.discovery.NacosDiscoveryClientConfiguration.class,
        com.alibaba.cloud.nacos.discovery.reactive.NacosReactiveDiscoveryClientConfiguration.class,
        com.alibaba.cloud.nacos.endpoint.NacosConfigEndpointAutoConfiguration.class,
        com.alibaba.cloud.nacos.registry.NacosServiceRegistryAutoConfiguration.class,
        org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration.class
    }
)
// @EnableDiscoveryClient  // 临时禁用进行诊断
public class GatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(GatewayApplication.class, args);
        System.out.println("========================================");
        System.out.println("  StudentMIS V2 API Gateway Started   ");
        System.out.println("  清华大学级学生成绩管理系统 - API网关  ");
        System.out.println("========================================");
    }
}
